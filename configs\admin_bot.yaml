agent:
  config:
    max_tokens: 1500
    temperature: 0.1
  model: gpt-4.1-mini
  type: langgraph
database:
  sql:
    connection_string: sqlite:///C:\Users\<USER>\Desktop\WORK\Agentic RAG\tests\test_db.sqlite
    tables:
      - staff
      - departments
      - budgets
      - facilities
      - events
description:
  A bot developed by Atlas University in Istanbul, Turkey, designed to
  assist administrators with institutional data and operations.
metadata:
  audience: administrators
  institution: Atlas University
  languages:
    - English
    - Turkish
  location: Istanbul, Turkey
  topics:
    - staff information
    - departmental data
    - budget analysis
    - facility management
    - institutional operations
name: AdminBot
prompts:
  query_prompt_path: admin_bot/query.txt
  system_prompt_path: admin_bot/system.txt
tools:
  - config:
      collection_name: admin_documents
      persist_directory: ./chroma_db/admin
      top_k: 5
    enabled: true
    type: DocumentSearchTool
  - config:
      allowed_tables:
        - staff
        - departments
        - budgets
        - facilities
      connection_string: sqlite:///C:\Users\<USER>\Desktop\WORK\Agentic RAG\tests\test_db.sqlite
      max_results: 50
    enabled: true
    type: SQLQueryTool
  - config:
      max_results: 3
      search_depth: comprehensive
    enabled: false
    type: WebSearchTool
