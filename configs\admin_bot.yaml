agent:
  config:
    max_tokens: 1500
    temperature: 0.1
  model: gpt-4.1-mini
  type: langgraph
database:
  sql:
    # Local SQLite database (default)
    connection_string: sqlite:///C:\Users\<USER>\Desktop\WORK\Agentic RAG\tests\test_db.sqlite

    # Remote database connection examples (uncomment and modify as needed):
    # PostgreSQL:
    # connection_string: postgresql://username:password@hostname:port/database_name
    # connection_string: postgresql://admin:admin123@localhost:5432/university_db

    # MySQL:
    # connection_string: mysql+pymysql://username:password@hostname:port/database_name
    # connection_string: mysql+pymysql://admin:admin123@localhost:3306/university_db

    # SQL Server:
    # connection_string: mssql+pyodbc://username:password@hostname:port/database_name?driver=ODBC+Driver+17+for+SQL+Server
    # connection_string: mssql+pyodbc://admin:admin123@localhost:1433/university_db?driver=ODBC+Driver+17+for+SQL+Server

    tables:
      - staff
      - departments
      - budgets
      - facilities
      - events
description:
  A bot developed by Atlas University in Istanbul, Turkey, designed to
  assist administrators with institutional data and operations.
metadata:
  audience: administrators
  institution: Atlas University
  languages:
    - English
    - Turkish
  location: Istanbul, Turkey
  topics:
    - staff information
    - departmental data
    - budget analysis
    - facility management
    - institutional operations
name: AdminBot
prompts:
  query_prompt_path: admin_bot/query.txt
  system_prompt_path: admin_bot/system.txt
tools:
  - config:
      collection_name: admin_documents
      persist_directory: ./chroma_db/admin
      top_k: 5
    enabled: true
    type: DocumentSearchTool
  - config:
      allowed_tables:
        - staff
        - departments
        - budgets
        - facilities
      # Local SQLite database (default)
      # connection_string: sqlite:///C:\Users\<USER>\Desktop\WORK\Agentic RAG\tests\test_db.sqlite

      # Remote database connection examples (uncomment and modify as needed):
      # PostgreSQL:
      # connection_string: postgresql://username:password@hostname:port/database_name
      connection_string: postgresql://testuser:<EMAIL>:5432/testdb

      # MySQL:
      # connection_string: mysql+pymysql://username:password@hostname:port/database_name
      # connection_string: mysql+pymysql://admin:admin123@localhost:3306/university_db

      # SQL Server:
      # connection_string: mssql+pyodbc://username:password@hostname:port/database_name?driver=ODBC+Driver+17+for+SQL+Server
      # connection_string: mssql+pyodbc://admin:admin123@localhost:1433/university_db?driver=ODBC+Driver+17+for+SQL+Server

      max_results: 50
    enabled: true
    type: SQLQueryTool
  - config:
      max_results: 3
      search_depth: comprehensive
    enabled: false
    type: WebSearchTool
